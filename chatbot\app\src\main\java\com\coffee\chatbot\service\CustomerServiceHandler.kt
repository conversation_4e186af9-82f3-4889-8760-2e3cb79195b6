package com.coffee.chatbot.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.content.Context
import android.graphics.*
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityNodeInfo

/**
 * 客服接待页面处理器 - 使用优化后的 XPath API
 * 保持原有接口兼容性，内部使用新的优雅 XPath 功能
 */
class CustomerServiceHandler(private val context: Context) {

    companion object {
        private const val TAG = "CustomerServiceHandler"
        private const val TARGET_PACKAGE = "com.xingin.eva"
        private const val CLICK_MARKER_DURATION = 2000L

        // 使用优化后的 XPath 表达式
        private const val CONVERSATION_LIST_XPATH = "//TextView[@text='当前会话']/../../ViewGroup[last()]/ScrollView[0]/ViewGroup[0]"
        // 新的未读消息标识 XPath (相对于会话项)
        private const val UNREAD_INDICATOR_XPATH = "/ViewGroup[1]/ViewGroup[0]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/TextView[0]"

        // 会话项内部元素的 XPath
        private const val NICKNAME_XPATH = "/ViewGroup[1]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/TextView[0]"
        private const val TIME_XPATH = "/ViewGroup[1]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/TextView[1]"
        private const val LAST_MESSAGE_XPATH = "/ViewGroup[1]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/TextView[2]"
    }

    private var accessibilityService: AccessibilityService? = null
    private val handler = Handler(Looper.getMainLooper())
    private var showClickMarker = true
    private var markerView: View? = null
    private val windowManager by lazy { context.getSystemService(Context.WINDOW_SERVICE) as WindowManager }

    // 会话详情页监听相关
    private var isChatDetailPageMonitoringEnabled = false
    private var lastChatDetailPageState = false
    private var chatDetailPageMonitorRunnable: Runnable? = null
    private var monitoringInterval = 2000L // 默认2秒检查一次
    private var lastChatSummary: String? = null

    fun setAccessibilityService(service: AccessibilityService) {
        this.accessibilityService = service
        Log.d(TAG, "✅ AccessibilityService 已设置")
    }

    /**
     * 主要入口方法：检查并点击有未读消息的会话
     */
    fun checkAndEnterNewMessage(): Boolean {
        Log.d(TAG, "🚀 === 开始检查未读消息 ===")

        val service = accessibilityService
        if (service == null) {
            Log.e(TAG, "❌ AccessibilityService 未设置")
            return false
        }

        val rootNode = service.rootInActiveWindow
        if (rootNode == null) {
            Log.w(TAG, "❌ 无法获取根节点")
            return false
        }

        try {
            // 验证是否在目标应用
            if (!isTargetApp(rootNode)) {
                Log.w(TAG, "❌ 不在目标应用中")
                return false
            }

            // 检查是否在客服接待页面
            if (!isInCustomerServicePage(rootNode)) {
                Log.w(TAG, "❌ 不在客服接待页面")
                return false
            }

            Log.d(TAG, "✅ 确认在客服接待页面")

            // 查找并点击有未读消息的会话
            val result = findAndClickUnreadConversation(rootNode)
            Log.d(TAG, "🚀 === 检查未读消息完成，结果: $result ===")
            return result

        } catch (e: Exception) {
            Log.e(TAG, "检查未读消息时发生异常", e)
            return false
        } finally {
            rootNode.recycle()
        }
    }

    /**
     * 验证是否在目标应用
     */
    private fun isTargetApp(rootNode: AccessibilityNodeInfo): Boolean {
        val packageName = rootNode.packageName?.toString()
        Log.d(TAG, "当前应用包名: $packageName")
        return packageName == TARGET_PACKAGE
    }

    /**
     * 检查是否在客服接待页面 - 使用优化后的 XPath API
     */
    private fun isInCustomerServicePage(rootNode: AccessibilityNodeInfo): Boolean {
        // 使用新的便捷方法查找"客服接待"文本
        val customerServiceNodes = rootNode.findAllByText("客服接待", exact = false)
        Log.d(TAG, "找到 ${customerServiceNodes.size} 个'客服接待'文本节点")

        val exists = customerServiceNodes.any { it.isVisibleToUser } // 检查是否有任何一个可见
        customerServiceNodes.recycle() // 自动资源管理
        return exists
    }

    /**
     * 查找并点击有未读消息的会话 - 使用优化后的 XPath API
     */
    private fun findAndClickUnreadConversation(rootNode: AccessibilityNodeInfo): Boolean {
        Log.d(TAG, "🎯 使用优化的 XPath API 查找会话列表")

        return rootNode.xpathUse(CONVERSATION_LIST_XPATH) { containerResult ->
            val conversationContainer = containerResult.first()
            if (conversationContainer == null) {
                Log.w(TAG, "❌ 未找到会话列表容器")
                return@xpathUse false
            }

            Log.d(TAG, "✅ 找到会话列表容器: childCount=${conversationContainer.childCount}")

            // 立即打印容器下的所有文本以供调试
            val allTextsResult = conversationContainer.findAll(className = "TextView")
            Log.d(TAG, "容器内所有文本: ${allTextsResult.texts().joinToString(" | ")}")
            allTextsResult.recycle()

            // 1. 直接遍历容器的所有子节点，每个子节点都是一个会话项
            val childCount = conversationContainer.childCount
            Log.d(TAG, "发现 $childCount 个会话项，正在打印详情...")
            for (i in 0 until childCount) {
                val conversationItem = conversationContainer.getChild(i)
                // 打印conversationItem中的文本
                conversationItem?.let { item ->
                    val texts = item.findAll(className = "TextView").texts()
                    Log.d(TAG, "会话项 $i 文本: ${texts.joinToString(" | ")}")
                    // 打印出TextView的相对conversationItem的层级关系
                    texts.forEach { text ->
                        val xpath = item.xpath(".//TextView[@text='$text']").first()?.getXPath() ?: "未找到"
                        Log.d(TAG, "  - '$text' XPath: $xpath")
                    }
                }
                if (conversationItem != null) {
                    Log.d(TAG, "--- 正在处理会话项 $i ---")
                    printConversationInfo(conversationItem)
                    
                    conversationItem.recycle() // getChild创建了新节点，需要回收
                }
            }

            // 2. 然后，继续执行原有的策略，查找并点击一个未读会话
            findAndClickUnreadConversationOptimized(conversationContainer)
        }
    }

    /**
     * 优化的未读会话查找和点击逻辑
     */
    private fun findAndClickUnreadConversationOptimized(container: AccessibilityNodeInfo): Boolean {
        Log.d(TAG, "🔍 使用新的 XPath 策略查找未读会话")

        val childCount = container.childCount
        Log.d(TAG, "会话容器中共有 $childCount 个会话项")

        for (i in 0 until childCount) {
            val conversationItem = container.getChild(i) ?: continue

            // 使用新的XPath检查未读标识
            val unreadResult = conversationItem.xpath(UNREAD_INDICATOR_XPATH)

            if (unreadResult.exists()) {
                Log.d(TAG, "✅ 策略成功：在会话项 $i 找到未读标识")
                unreadResult.recycle() // The result is not needed anymore

                // 使用找到的会话项调用点击函数
                // clickFirstUnreadConversation 会回收 conversationItem
                return clickFirstUnreadConversation(XPathResult(listOf(conversationItem)))
            }

            unreadResult.recycle()
            conversationItem.recycle()
        }

        Log.w(TAG, "❌ 新的 XPath 策略未找到任何未读会话")
        return false
    }

    /**
     * 点击第一个未读会话 - 使用优化的方法
     */
    private fun clickFirstUnreadConversation(conversations: XPathResult): Boolean {
        // 点击第一个对用户可见的会话
        val firstConversation = conversations.firstOrNull { it.isVisibleToUser } ?: return false

        Log.d(TAG, "📱 准备点击第一个会话")

        // 打印会话信息用于调试
        printConversationInfo(firstConversation)

        // 使用多种策略查找最佳点击目标
        val success = performSmartClick(firstConversation)

        // 验证点击是否成功（检查页面是否发生变化）
        if (success) {
            val verified = verifyClickSuccess()
            if (verified) {
                Log.d(TAG, "✅ 点击成功，页面已跳转")
            } else {
                Log.w(TAG, "⚠️ 点击执行了但页面可能未跳转")
            }
        }

        conversations.recycle() // 清理资源
        return success
    }

    /**
     * 智能点击策略 - 尝试多种方法找到最佳点击位置
     */
    private fun performSmartClick(conversation: AccessibilityNodeInfo): Boolean {
        Log.d(TAG, "🎯 开始智能点击策略")

        // 策略1: 查找会话中间区域的可点击元素（通常是主要内容区域）
        val centerClickable = findCenterClickableArea(conversation)
        if (centerClickable != null) {
            Log.d(TAG, "策略1: 找到中心可点击区域")
            val success = performClickOnNode(centerClickable)
            centerClickable.recycle()
            if (success) return true
        }

        // 策略2: 查找昵称区域（通常可点击）
        val nicknameArea = conversation.xpath(NICKNAME_XPATH).first()
        if (nicknameArea != null) {
            Log.d(TAG, "策略2: 尝试点击昵称区域")
            val success = performClickOnNode(nicknameArea)
            if (success) return true
        }

        // 策略3: 查找任何可点击的子元素
        val anyClickable = conversation.xpath(".//[@clickable='true']").first()
        if (anyClickable != null) {
            Log.d(TAG, "策略3: 找到可点击子元素")
            val success = performClickOnNode(anyClickable)
            if (success) return true
        }

        // 策略4: 直接点击会话容器
        Log.d(TAG, "策略4: 直接点击会话容器")
        return performClickOnNode(conversation)
    }

    /**
     * 查找会话中心区域的可点击元素
     */
    private fun findCenterClickableArea(conversation: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        val conversationBounds = Rect()
        conversation.getBoundsInScreen(conversationBounds)

        val centerY = conversationBounds.centerY()
        val tolerance = conversationBounds.height() / 4 // 允许25%的误差

        // 查找Y坐标接近中心的可点击元素
        val clickableElements = conversation.xpath(".//[@clickable='true']")

        for (element in clickableElements) {
            val elementBounds = Rect()
            element.getBoundsInScreen(elementBounds)

            if (Math.abs(elementBounds.centerY() - centerY) <= tolerance) {
                Log.d(TAG, "找到中心区域可点击元素: ${element.describe()}")
                clickableElements.recycle()
                return element
            }
        }

        clickableElements.recycle()
        return null
    }

    /**
     * 打印会话信息用于调试 - 使用优化的方法
     */
    private fun printConversationInfo(conversation: AccessibilityNodeInfo) {
        // 使用您提供的精确 XPath 路径进行链式查询
        val nicknameResult = conversation.xpath(NICKNAME_XPATH)
        val timeResult = conversation.xpath(TIME_XPATH)
        val lastMessageResult = conversation.xpath(LAST_MESSAGE_XPATH)

        val nickname = nicknameResult.text()
        val time = timeResult.text()
        val lastMessage = lastMessageResult.text()

        Log.d(TAG, "会话信息:")
        Log.d(TAG, "  昵称: '${nickname ?: "未找到"}'")
        Log.d(TAG, "  时间: '${time ?: "未找到"}'")
        Log.d(TAG, "  最后消息: '${lastMessage ?: "未找到"}'")

        // 检查是否有未读标识
        // 检查是否有未读标识
        val unreadResult = conversation.xpath(UNREAD_INDICATOR_XPATH)
        val hasUnread = unreadResult.exists()
        Log.d(TAG, "  未读状态: $hasUnread")
        unreadResult.recycle()

        // 回收所有查询结果
        nicknameResult.recycle()
        timeResult.recycle()
        lastMessageResult.recycle()
    }

    /**
     * 在节点上执行点击操作 - 增强版本
     */
    private fun performClickOnNode(node: AccessibilityNodeInfo): Boolean {
        Log.d(TAG, "🎯 尝试点击节点: ${node.describe()}")

        val bounds = Rect()
        node.getBoundsInScreen(bounds)

        if (bounds.isEmpty) {
            Log.e(TAG, "❌ 节点边界为空，无法点击")
            return false
        }

        Log.d(TAG, "节点边界: $bounds")

        // 策略 1: 直接使用 performAction(ACTION_CLICK)
        if (node.isClickable && node.performAction(AccessibilityNodeInfo.ACTION_CLICK)) {
            Log.d(TAG, "✅ ACTION_CLICK 成功")
            showClickMarkerIfEnabled(bounds.centerX(), bounds.centerY())
            Thread.sleep(300) // 给系统反应时间
            return true
        }

        // 策略 2: 查找可点击的父节点
        val clickableParent = findClickableParent(node)
        if (clickableParent != null && clickableParent != node) {
            Log.d(TAG, "🔍 找到可点击父节点: ${clickableParent.describe()}")
            val success = clickableParent.performAction(AccessibilityNodeInfo.ACTION_CLICK)
            if (success) {
                Log.d(TAG, "✅ 父节点 ACTION_CLICK 成功")
                val parentBounds = Rect()
                clickableParent.getBoundsInScreen(parentBounds)
                showClickMarkerIfEnabled(parentBounds.centerX(), parentBounds.centerY())
                clickableParent.recycle()
                Thread.sleep(300)
                return true
            }
            clickableParent.recycle()
        }

        // 策略 3: 尝试多个位置的手势点击
        Log.w(TAG, "⚠️ ACTION_CLICK 失败，尝试手势点击")
        return performMultiPositionGestureClick(bounds)
    }

    /**
     * 查找可点击的父节点
     */
    private fun findClickableParent(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        var current: AccessibilityNodeInfo? = node.parent
        var depth = 0
        val maxDepth = 5 // 限制搜索深度

        while (current != null && depth < maxDepth) {
            if (current.isClickable) {
                return current
            }
            val parent = current.parent
            current.recycle()
            current = parent
            depth++
        }

        current?.recycle()
        return null
    }

    /**
     * 尝试多个位置的手势点击
     */
    private fun performMultiPositionGestureClick(bounds: Rect): Boolean {
        val positions = listOf(
            // 中心位置
            Pair(bounds.centerX(), bounds.centerY()),
            // 稍微偏左的位置（避开可能的按钮）
            Pair(bounds.left + bounds.width() / 3, bounds.centerY()),
            // 稍微偏右的位置
            Pair(bounds.right - bounds.width() / 3, bounds.centerY()),
            // 稍微偏上的位置
            Pair(bounds.centerX(), bounds.top + bounds.height() / 3)
        )

        for ((index, position) in positions.withIndex()) {
            val (x, y) = position
            Log.d(TAG, "🎯 尝试位置 ${index + 1}: ($x, $y)")

            showClickMarkerIfEnabled(x, y)

            if (performGestureClick(x, y)) {
                Log.d(TAG, "✅ 位置 ${index + 1} 手势点击成功")
                Thread.sleep(500) // 给更多时间让界面响应
                return true
            }

            Thread.sleep(200) // 短暂延时再尝试下一个位置
        }

        Log.e(TAG, "❌ 所有位置的手势点击都失败了")
        return false
    }

    /**
     * 显示点击标记（如果启用）
     */
    private fun showClickMarkerIfEnabled(x: Int, y: Int) {
        if (showClickMarker) {
            showClickMarker(x, y)
        }
    }

    /**
     * 执行手势点击 - 增强版本
     */
    private fun performGestureClick(x: Int, y: Int): Boolean {
        val service = accessibilityService
        if (service == null) {
            Log.e(TAG, "❌ AccessibilityService 为空")
            return false
        }

        try {
            val path = Path().apply {
                moveTo(x.toFloat(), y.toFloat())
            }

            val gestureBuilder = GestureDescription.Builder()
            // 增加点击持续时间，模拟更真实的点击
            val strokeDescription = GestureDescription.StrokeDescription(path, 0, 150)
            gestureBuilder.addStroke(strokeDescription)

            val gesture = gestureBuilder.build()

            var gestureCompleted = false
            var gestureCancelled = false

            val success = service.dispatchGesture(gesture, object : AccessibilityService.GestureResultCallback() {
                override fun onCompleted(gestureDescription: GestureDescription?) {
                    Log.d(TAG, "✅ 手势点击完成 at ($x, $y)")
                    gestureCompleted = true
                }

                override fun onCancelled(gestureDescription: GestureDescription?) {
                    Log.w(TAG, "❌ 手势点击被取消 at ($x, $y)")
                    gestureCancelled = true
                }
            }, null)

            if (!success) {
                Log.e(TAG, "❌ 手势分发失败 at ($x, $y)")
                return false
            }

            // 等待手势完成，最多等待1秒
            val startTime = System.currentTimeMillis()
            while (!gestureCompleted && !gestureCancelled &&
                   (System.currentTimeMillis() - startTime) < 1000) {
                Thread.sleep(50)
            }

            return gestureCompleted

        } catch (e: Exception) {
            Log.e(TAG, "❌ 执行手势点击时发生异常 at ($x, $y)", e)
            return false
        }
    }

    /**
     * 验证点击是否成功（通过检查页面变化）
     */
    private fun verifyClickSuccess(): Boolean {
        // 等待页面跳转
        Thread.sleep(1000)

        val service = accessibilityService ?: return false
        val rootNode = service.rootInActiveWindow ?: return false

        try {
            // 检查是否还在客服接待页面
            val stillInCustomerService = rootNode.findAllByText("客服接待", exact = false).exists()

            if (!stillInCustomerService) {
                Log.d(TAG, "✅ 页面已跳转，不再是客服接待页面")
                return true
            }

            // 检查是否出现了聊天界面的特征元素
            val hasChatFeatures = checkForChatFeatures(rootNode)
            if (hasChatFeatures) {
                Log.d(TAG, "✅ 检测到聊天界面特征")
                return true
            }

            Log.w(TAG, "⚠️ 仍在客服接待页面，点击可能未成功")
            return false

        } catch (e: Exception) {
            Log.e(TAG, "❌ 验证点击成功时发生异常", e)
            return false
        } finally {
            rootNode.recycle()
        }
    }

    /**
     * 检查聊天界面的特征元素
     */
    private fun checkForChatFeatures(rootNode: AccessibilityNodeInfo): Boolean {
        // 检查常见的聊天界面元素
        val chatFeatures = listOf(
            "//EditText", // 输入框
            "//[@text~='发送']", // 发送按钮
            "//[@desc~='发送']", // 发送按钮描述
            "//[@text~='输入']", // 输入提示
            "//[@desc~='消息']" // 消息相关描述
        )

        for (xpath in chatFeatures) {
            if (rootNode.xpath(xpath).exists()) {
                Log.d(TAG, "找到聊天特征: $xpath")
                return true
            }
        }

        return false
    }

    /**
     * 处理消息输入和发送 - 展示新 API 用法
     */
    fun handleMessageInput(rootNode: AccessibilityNodeInfo, message: String): Boolean {
        Log.d(TAG, "📝 处理消息输入: $message")

        return rootNode.xpathUse("//EditText[@enabled='true']") { inputFields ->
            val inputField = inputFields.first()
            if (inputField == null) {
                Log.w(TAG, "❌ 未找到输入框")
                return@xpathUse false
            }

            // 输入文本
            val inputSuccess = inputField.performAction(
                AccessibilityNodeInfo.ACTION_SET_TEXT,
                android.os.Bundle().apply {
                    putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, message)
                }
            )

            if (!inputSuccess) {
                Log.w(TAG, "❌ 输入文本失败")
                return@xpathUse false
            }

            // 查找并点击发送按钮
            val sendButton = rootNode.findSendButton()
            if (sendButton == null) {
                Log.w(TAG, "❌ 未找到发送按钮")
                return@xpathUse false
            }

            val sendSuccess = sendButton?.performAction(AccessibilityNodeInfo.ACTION_CLICK) ?: false
            Log.d(TAG, if (sendSuccess) "✅ 消息发送成功" else "❌ 消息发送失败")

            sendSuccess
        }
    }

    /**
     * 调试方法：分析页面结构
     */
    fun analyzePageStructure(rootNode: AccessibilityNodeInfo) {
        Log.d(TAG, "🔍 分析页面结构")

        // 打印节点树结构
        rootNode.printTree(maxDepth = 3)

        // 查找所有可点击元素
        val clickableElements = rootNode.findClickable()
        Log.d(TAG, "找到 ${clickableElements.size} 个可点击元素:")
        clickableElements.forEach { element ->
            Log.d(TAG, "  - ${element.describe()}")
        }

        // 查找所有文本元素
        val textElements = rootNode.findAll(className = "TextView")
        Log.d(TAG, "找到 ${textElements.size} 个文本元素:")
        textElements.texts().forEach { text ->
            if (text.isNotBlank()) {
                Log.d(TAG, "  - '$text'")
            }
        }

        // 清理资源
        clickableElements.recycle()
        textElements.recycle()
    }

    // ===== 点击标记相关方法 =====

    /**
     * 显示点击标记
     */
    private fun showClickMarker(x: Int, y: Int) {
        if (!showClickMarker) return

        try {
            // 移除之前的标记
            removeClickMarker()

            // 创建新的标记视图
            markerView = View(context).apply {
                setBackgroundColor(Color.RED)
                alpha = 0.8f
            }

            val params = WindowManager.LayoutParams().apply {
                width = 20
                height = 20
                this.x = x - 10
                this.y = y - 10
                type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                format = PixelFormat.TRANSLUCENT
                gravity = Gravity.TOP or Gravity.START
            }

            windowManager.addView(markerView, params)

            // 延时移除标记
            handler.postDelayed({
                removeClickMarker()
            }, CLICK_MARKER_DURATION)

        } catch (e: Exception) {
            Log.e(TAG, "显示点击标记失败", e)
        }
    }

    /**
     * 移除点击标记
     */
    private fun removeClickMarker() {
        markerView?.let { view ->
            // 只有当视图真正附加到窗口时才尝试移除
            if (view.isAttachedToWindow) {
                try {
                    windowManager.removeView(view)
                } catch (e: Exception) {
                    Log.w(TAG, "移除点击标记失败", e)
                }
            }
            markerView = null
        }
    }

    /**
     * 设置是否显示点击标记
     */
    fun setShowClickMarker(show: Boolean) {
        showClickMarker = show
        Log.d(TAG, "点击标记显示: $show")
    }

    /**
     * 启用会话详情页监听
     * @param intervalMs 监听间隔时间（毫秒），默认2000ms
     */
    fun enableChatDetailPageMonitoring(intervalMs: Long = 2000L) {
        if (isChatDetailPageMonitoringEnabled) {
            Log.w(TAG, "⚠️ 会话详情页监听已经启用")
            return
        }

        monitoringInterval = intervalMs
        isChatDetailPageMonitoringEnabled = true
        lastChatDetailPageState = false
        lastChatSummary = null

        Log.d(TAG, "🎯 启用会话详情页监听，间隔: ${intervalMs}ms")
        startChatDetailPageMonitoring()
    }

    /**
     * 禁用会话详情页监听
     */
    fun disableChatDetailPageMonitoring() {
        if (!isChatDetailPageMonitoringEnabled) {
            Log.w(TAG, "⚠️ 会话详情页监听未启用")
            return
        }

        isChatDetailPageMonitoringEnabled = false
        stopChatDetailPageMonitoring()

        Log.d(TAG, "🛑 已禁用会话详情页监听")
    }

    /**
     * 检查监听状态
     */
    fun isChatDetailPageMonitoringEnabled(): Boolean {
        return isChatDetailPageMonitoringEnabled
    }

    /**
     * 设置监听间隔
     */
    fun setChatDetailPageMonitoringInterval(intervalMs: Long) {
        monitoringInterval = intervalMs
        Log.d(TAG, "⏱️ 设置监听间隔: ${intervalMs}ms")

        // 如果监听正在运行，重启以应用新间隔
        if (isChatDetailPageMonitoringEnabled) {
            stopChatDetailPageMonitoring()
            startChatDetailPageMonitoring()
        }
    }

    /**
     * 开始会话详情页监听
     */
    private fun startChatDetailPageMonitoring() {
        chatDetailPageMonitorRunnable = object : Runnable {
            override fun run() {
                if (isChatDetailPageMonitoringEnabled) {
                    performChatDetailPageCheck()
                    // 继续下一次检查
                    handler.postDelayed(this, monitoringInterval)
                }
            }
        }

        // 立即执行第一次检查
        handler.post(chatDetailPageMonitorRunnable!!)
        Log.d(TAG, "🔄 会话详情页监听已启动")
    }

    /**
     * 停止会话详情页监听
     */
    private fun stopChatDetailPageMonitoring() {
        chatDetailPageMonitorRunnable?.let { runnable ->
            handler.removeCallbacks(runnable)
            chatDetailPageMonitorRunnable = null
        }
        Log.d(TAG, "⏹️ 会话详情页监听已停止")
    }

    /**
     * 执行会话详情页检查
     */
    private fun performChatDetailPageCheck() {
        try {
            val service = accessibilityService
            if (service == null) {
                // Log.v(TAG, "AccessibilityService 未设置，跳过检查")
                return
            }

            val rootNode = service.rootInActiveWindow
            if (rootNode == null) {
                // Log.v(TAG, "无法获取根节点，跳过检查")
                return
            }

            try {
                // 检查是否在会话详情页
                val messageInputResult = rootNode.xpath("//EditText[@text='发消息...']")
                val isInChatPage = messageInputResult.exists()
                messageInputResult.recycle()

                // 检查状态是否发生变化
                if (isInChatPage != lastChatDetailPageState) {
                    lastChatDetailPageState = isInChatPage

                    if (isInChatPage) {
                        Log.d(TAG, "🎯 === 监听检测：进入会话详情页 ===")
                        handleEnterChatDetailPage(rootNode)
                    } else {
                        Log.d(TAG, "🚪 === 监听检测：离开会话详情页 ===")
                        handleLeaveChatDetailPage()
                    }
                } else if (isInChatPage) {
                    // 仍在会话详情页，检查会话内容是否有变化
                    checkChatContentChanges(rootNode)
                }

            } finally {
                rootNode.recycle()
            }

        } catch (e: Exception) {
            Log.e(TAG, "会话详情页监听检查时发生异常", e)
        }
    }

    /**
     * 处理进入会话详情页
     */
    private fun handleEnterChatDetailPage(rootNode: AccessibilityNodeInfo) {
        Log.d(TAG, "📋 正在获取会话概要...")

        val summary = getChatHistorySummaryInternal(rootNode)
        lastChatSummary = summary

        if (summary != null) {
            Log.d(TAG, "✅ 会话概要获取成功")
            Log.d(TAG, "📝 === 会话记录概要 ===")

            // 在日志中显示会话概要
            displayChatSummaryInLog(summary)

            Log.d(TAG, "📝 === 会话记录概要结束 ===")
        } else {
            Log.w(TAG, "⚠️ 会话概要获取失败或为空会话")
        }
    }

    /**
     * 处理离开会话详情页
     */
    private fun handleLeaveChatDetailPage() {
        lastChatSummary = null
        Log.d(TAG, "ℹ️ 已离开会话详情页")
    }

    /**
     * 检查会话内容变化
     */
    private fun checkChatContentChanges(rootNode: AccessibilityNodeInfo) {
        val currentSummary = getChatHistorySummaryInternal(rootNode)

        if (currentSummary != lastChatSummary) {
            Log.d(TAG, "🔄 === 监听检测：会话内容发生变化 ===")
            lastChatSummary = currentSummary

            if (currentSummary != null) {
                Log.d(TAG, "📝 === 更新的会话记录概要 ===")
                displayChatSummaryInLog(currentSummary)
                Log.d(TAG, "📝 === 更新的会话记录概要结束 ===")
            }
        }
    }

    /**
     * 在日志中显示会话概要
     */
    private fun displayChatSummaryInLog(summary: String) {
        val lines = summary.split("\n")
        Log.d(TAG, "会话记录总行数: ${lines.size}")

        lines.forEachIndexed { index, line ->
            if (line.trim().isNotEmpty()) {
                Log.d(TAG, "[$index] $line")
            }
        }

        // 统计信息
        val messageLines = lines.filter { it.startsWith("- ") }
        Log.d(TAG, "📊 统计信息: 共 ${messageLines.size} 条消息记录")
    }

    /**
     * 检查是否进入会话详情页
     * 使用xpath语法检查是否存在包含"发消息..."文本的EditText
     * 如果进入会话详情页，会自动获取会话概要
     * @return 是否在会话详情页
     */
    fun isInChatDetailPage(): Boolean {
        val service = accessibilityService
        if (service == null) {
            Log.e(TAG, "❌ AccessibilityService 未设置")
            return false
        }

        val rootNode = service.rootInActiveWindow
        if (rootNode == null) {
            Log.w(TAG, "❌ 无法获取根节点")
            return false
        }

        try {
            // 使用xpath查找包含"发消息..."文本的EditText
            val messageInputResult = rootNode.xpath("//EditText[@text='发消息...']")
            val isInChatPage = messageInputResult.exists()

            if (isInChatPage) {
                Log.d(TAG, "✅ 已进入会话详情页")
                // 进入会话详情页时，立即获取会话概要
                Log.d(TAG, "🔄 自动获取会话概要...")
                val summary = getChatHistorySummaryInternal(rootNode)
                if (summary != null) {
                    Log.d(TAG, "✅ 会话概要获取成功")
                    Log.d(TAG, "📋 会话概要内容:\n$summary")
                } else {
                    Log.w(TAG, "⚠️ 会话概要获取失败")
                }
            } else {
                Log.d(TAG, "❌ 未在会话详情页")
            }

            messageInputResult.recycle()
            return isInChatPage

        } catch (e: Exception) {
            Log.e(TAG, "检查会话详情页时发生异常", e)
            return false
        } finally {
            rootNode.recycle()
        }
    }

    /**
     * 获取会话记录概要
     * 从 //EditText[@text='发消息...']/../../../ScrollView[0] 获取文本作为会话记录的概要
     * @return 会话记录概要文本，如果获取失败返回null
     */
    fun getChatHistorySummary(): String? {
        val service = accessibilityService
        if (service == null) {
            Log.e(TAG, "❌ AccessibilityService 未设置")
            return null
        }

        val rootNode = service.rootInActiveWindow
        if (rootNode == null) {
            Log.w(TAG, "❌ 无法获取根节点")
            return null
        }

        try {
            return getChatHistorySummaryInternal(rootNode)
        } catch (e: Exception) {
            Log.e(TAG, "获取会话记录概要时发生异常", e)
            return null
        } finally {
            rootNode.recycle()
        }
    }

    /**
     * 内部方法：获取会话记录概要
     * @param rootNode 根节点，由调用方管理生命周期
     * @return 会话记录概要文本，如果获取失败返回null
     */
    private fun getChatHistorySummaryInternal(rootNode: AccessibilityNodeInfo): String? {
        try {
            // 使用xpath查找会话记录容器
            val chatHistoryResult = rootNode.xpath("//EditText[@text='发消息...']/../../../ScrollView[0]")
            val chatHistoryContainer = chatHistoryResult.first()

            if (chatHistoryContainer == null) {
                Log.w(TAG, "❌ 未找到会话记录容器")
                chatHistoryResult.recycle()
                return null
            }

            Log.d(TAG, "✅ 找到会话记录容器")

            // 获取容器内所有文本内容
            val allTextNodes = chatHistoryContainer.findAll(className = "TextView")
            val textList = allTextNodes.texts().filter { it.isNotBlank() }

            Log.d(TAG, "📝 会话记录包含 ${textList.size} 条文本内容")

            // 将所有文本组合成概要
            val summary = if (textList.isNotEmpty()) {
                textList.joinToString("\n") { "- $it" }
            } else {
                "暂无会话记录"
            }

            // 清理资源
            allTextNodes.recycle()
            chatHistoryResult.recycle()

            return summary

        } catch (e: Exception) {
            Log.e(TAG, "获取会话记录概要时发生异常", e)
            return null
        }
    }

    /**
     * 进入会话详情页时的处理方法
     * 检测到进入会话详情页时，立即获取会话概要并返回完整信息
     * @return 包含页面状态和会话概要的结果对象
     */
    fun onEnterChatDetailPage(): ChatDetailResult {
        Log.d(TAG, "🚀 === 进入会话详情页处理 ===")

        val service = accessibilityService
        if (service == null) {
            Log.e(TAG, "❌ AccessibilityService 未设置")
            return ChatDetailResult(false, null)
        }

        val rootNode = service.rootInActiveWindow
        if (rootNode == null) {
            Log.w(TAG, "❌ 无法获取根节点")
            return ChatDetailResult(false, null)
        }

        try {
            // 检查是否在会话详情页
            val messageInputResult = rootNode.xpath("//EditText[@text='发消息...']")
            val isInChatPage = messageInputResult.exists()
            messageInputResult.recycle()

            if (!isInChatPage) {
                Log.w(TAG, "❌ 不在会话详情页")
                return ChatDetailResult(false, null)
            }

            Log.d(TAG, "✅ 确认在会话详情页")

            // 立即获取会话概要
            Log.d(TAG, "📋 正在获取会话概要...")
            val summary = getChatHistorySummaryInternal(rootNode)

            val result = ChatDetailResult(
                isInChatDetailPage = true,
                chatHistorySummary = summary
            )

            Log.d(TAG, "🚀 === 会话详情页处理完成 ===")
            Log.d(TAG, "概要获取: ${if (summary != null) "成功" else "失败"}")

            if (summary != null) {
                Log.d(TAG, "📝 会话概要预览:")
                val lines = summary.split("\n").take(5) // 只显示前5行
                lines.forEach { line -> Log.d(TAG, "  $line") }
                if (summary.split("\n").size > 5) {
                    Log.d(TAG, "  ... (还有更多内容)")
                }
            }

            return result

        } catch (e: Exception) {
            Log.e(TAG, "进入会话详情页处理时发生异常", e)
            return ChatDetailResult(false, null)
        } finally {
            rootNode.recycle()
        }
    }

    /**
     * 检查并获取会话详情信息
     * 综合方法：先检查是否在会话详情页，如果是则获取会话记录概要
     * @return 包含页面状态和会话概要的结果对象
     */
    fun checkAndGetChatDetails(): ChatDetailResult {
        Log.d(TAG, "🔍 === 开始检查会话详情 ===")

        val isInChatPage = isInChatDetailPage()
        val summary = if (isInChatPage) {
            // 注意：isInChatDetailPage()已经获取过概要了，这里避免重复获取
            Log.d(TAG, "ℹ️ 页面检查时已获取概要，跳过重复获取")
            null // 可以考虑缓存之前获取的结果
        } else {
            null
        }

        val result = ChatDetailResult(
            isInChatDetailPage = isInChatPage,
            chatHistorySummary = summary
        )

        Log.d(TAG, "🔍 === 会话详情检查完成 ===")
        Log.d(TAG, "页面状态: ${if (result.isInChatDetailPage) "会话详情页" else "非会话详情页"}")

        return result
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        // 停止会话详情页监听
        disableChatDetailPageMonitoring()

        // 清理点击标记
        removeClickMarker()

        Log.d(TAG, "资源清理完成")
    }
}

/**
 * 会话详情检查结果数据类
 */
data class ChatDetailResult(
    val isInChatDetailPage: Boolean,
    val chatHistorySummary: String?
)

// ===== 扩展函数：为客服场景定制的便捷方法 =====

/**
 * 查找发送按钮的多种策略
 */
