package com.coffee.chatbot.service

import android.accessibilityservice.AccessibilityService
import android.util.Log
import android.view.accessibility.AccessibilityEvent

/**
 * 会话详情页检测功能使用示例
 * 
 * 这个示例展示了如何在AccessibilityService中使用新增的会话详情检测功能
 */
class ChatDetailDetectionExample : AccessibilityService() {

    companion object {
        private const val TAG = "ChatDetailExample"
    }

    private lateinit var customerServiceHandler: CustomerServiceHandler

    override fun onServiceConnected() {
        super.onServiceConnected()
        Log.d(TAG, "AccessibilityService 已连接")
        
        // 初始化CustomerServiceHandler
        customerServiceHandler = CustomerServiceHandler(this)
        customerServiceHandler.setAccessibilityService(this)
        
        // 启用点击标记以便调试
        customerServiceHandler.setShowClickMarker(true)
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        event?.let { 
            // 当窗口内容发生变化时，检查会话详情页状态
            if (it.eventType == AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED ||
                it.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
                
                // 延迟一点时间让UI稳定
                android.os.Handler(mainLooper).postDelayed({
                    checkChatDetailStatus()
                }, 500)
            }
        }
    }

    override fun onInterrupt() {
        Log.d(TAG, "AccessibilityService 被中断")
        customerServiceHandler.cleanup()
    }

    /**
     * 检查会话详情页状态的示例方法
     */
    private fun checkChatDetailStatus() {
        try {
            Log.d(TAG, "=== 开始检查会话详情页状态 ===")

            // 推荐方法: 使用专门的进入会话详情页处理方法
            val result = customerServiceHandler.onEnterChatDetailPage()

            when {
                result.isInChatDetailPage && result.chatHistorySummary != null -> {
                    Log.d(TAG, "✅ 成功进入会话详情页并获取概要")

                    // 处理会话记录
                    processChatHistory(result.chatHistorySummary)

                    // 分析会话内容
                    analyzeChatHistory(result.chatHistorySummary)

                    // 可以在这里添加自动回复逻辑
                    // autoReplyIfNeeded(result.chatHistorySummary)
                }

                result.isInChatDetailPage && result.chatHistorySummary == null -> {
                    Log.w(TAG, "⚠️ 在会话详情页但无法获取会话记录")
                    // 可能是空会话或者UI结构发生了变化
                    handleEmptyChat()
                }

                else -> {
                    Log.d(TAG, "ℹ️ 不在会话详情页")
                    // 可以尝试其他页面的处理逻辑
                    handleOtherPages()
                }
            }

            // 可选: 演示其他检查方法
            // demonstrateOtherMethods()

        } catch (e: Exception) {
            Log.e(TAG, "检查会话详情页状态时发生异常", e)
        }
    }

    /**
     * 处理空会话的情况
     */
    private fun handleEmptyChat() {
        Log.d(TAG, "--- 处理空会话 ---")
        // 可以在这里添加处理空会话的逻辑
        // 例如：发送欢迎消息、等待用户输入等
    }

    /**
     * 处理其他页面的情况
     */
    private fun handleOtherPages() {
        Log.d(TAG, "--- 处理其他页面 ---")
        // 可以在这里添加处理其他页面的逻辑
        // 例如：检查是否在客服接待页面、导航到会话页面等
    }

    /**
     * 演示其他检查方法的使用
     */
    private fun demonstrateOtherMethods() {
        Log.d(TAG, "--- 演示其他检查方法 ---")

        // 方法1: 单独检查页面状态
        val isInChatPage = customerServiceHandler.isInChatDetailPage()
        Log.d(TAG, "单独页面检查: ${if (isInChatPage) "在会话详情页" else "不在会话详情页"}")

        // 方法2: 单独获取概要
        if (isInChatPage) {
            val summary = customerServiceHandler.getChatHistorySummary()
            Log.d(TAG, "单独概要获取: ${if (summary != null) "成功" else "失败"}")
        }

        // 方法3: 使用综合检查方法
        demonstrateComprehensiveCheck()
    }

    /**
     * 演示综合检查方法的使用
     */
    private fun demonstrateComprehensiveCheck() {
        Log.d(TAG, "--- 演示综合检查方法 ---")
        
        val result = customerServiceHandler.checkAndGetChatDetails()
        
        when {
            result.isInChatDetailPage && result.chatHistorySummary != null -> {
                Log.d(TAG, "✅ 完美：在会话详情页且成功获取会话记录")
                Log.d(TAG, "会话记录长度: ${result.chatHistorySummary.length} 字符")
                
                // 分析会话记录
                analyzeChatHistory(result.chatHistorySummary)
                
                // 可以在这里实现自动回复逻辑
                // autoReplyIfNeeded(result.chatHistorySummary)
            }
            
            result.isInChatDetailPage && result.chatHistorySummary == null -> {
                Log.w(TAG, "⚠️ 在会话详情页但无法获取会话记录")
                // 可能是空会话或者UI结构发生了变化
            }
            
            else -> {
                Log.d(TAG, "ℹ️ 不在会话详情页，无需处理")
            }
        }
    }

    /**
     * 处理会话记录的示例方法
     */
    private fun processChatHistory(summary: String) {
        Log.d(TAG, "--- 处理会话记录 ---")
        
        // 将概要按行分割
        val lines = summary.split("\n").filter { it.trim().isNotEmpty() }
        Log.d(TAG, "会话记录包含 ${lines.size} 行内容")
        
        // 统计消息数量
        val messageLines = lines.filter { it.startsWith("- ") }
        Log.d(TAG, "其中消息行数: ${messageLines.size}")
        
        // 提取消息内容（去掉"- "前缀）
        val messages = messageLines.map { it.substring(2) }
        messages.forEachIndexed { index, message ->
            Log.d(TAG, "消息 ${index + 1}: $message")
        }
        
        // 可以在这里添加更复杂的消息分析逻辑
        // 例如：识别问题类型、提取关键词、判断是否需要回复等
    }

    /**
     * 分析会话记录的示例方法
     */
    private fun analyzeChatHistory(summary: String) {
        Log.d(TAG, "--- 分析会话记录 ---")
        
        // 简单的关键词分析
        val keywords = listOf("问题", "帮助", "咨询", "投诉", "建议")
        val foundKeywords = keywords.filter { summary.contains(it) }
        
        if (foundKeywords.isNotEmpty()) {
            Log.d(TAG, "发现关键词: ${foundKeywords.joinToString(", ")}")
            // 可以根据关键词类型采取不同的处理策略
        }
        
        // 检查是否有未回复的消息
        if (summary.contains("客户") && !summary.contains("客服")) {
            Log.d(TAG, "⚠️ 可能有未回复的客户消息")
            // 可以触发提醒或自动回复
        }
        
        // 统计会话活跃度
        val messageCount = summary.split("\n").count { it.startsWith("- ") }
        when {
            messageCount > 10 -> Log.d(TAG, "📈 高活跃度会话 ($messageCount 条消息)")
            messageCount > 5 -> Log.d(TAG, "📊 中等活跃度会话 ($messageCount 条消息)")
            else -> Log.d(TAG, "📉 低活跃度会话 ($messageCount 条消息)")
        }
    }

    /**
     * 自动回复示例（注释掉以避免意外发送）
     */
    private fun autoReplyIfNeeded(summary: String) {
        // 注意：这只是示例代码，实际使用时需要谨慎
        /*
        if (shouldAutoReply(summary)) {
            val reply = generateAutoReply(summary)
            val rootNode = rootInActiveWindow
            if (rootNode != null) {
                customerServiceHandler.handleMessageInput(rootNode, reply)
                Log.d(TAG, "✅ 已发送自动回复: $reply")
            }
        }
        */
        Log.d(TAG, "自动回复功能已禁用（示例代码）")
    }

    /**
     * 判断是否需要自动回复
     */
    private fun shouldAutoReply(summary: String): Boolean {
        // 这里可以实现复杂的判断逻辑
        // 例如：检查最后一条消息是否来自客户、是否包含问号、时间间隔等
        return false // 示例中总是返回false
    }

    /**
     * 生成自动回复内容
     */
    private fun generateAutoReply(summary: String): String {
        // 这里可以实现智能回复生成逻辑
        // 例如：基于关键词匹配、调用AI接口等
        return "感谢您的咨询，我们会尽快为您处理。"
    }

    override fun onDestroy() {
        super.onDestroy()
        customerServiceHandler.cleanup()
        Log.d(TAG, "AccessibilityService 已销毁")
    }
}
