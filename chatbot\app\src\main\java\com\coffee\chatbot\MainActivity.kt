package com.coffee.chatbot

import android.accessibilityservice.AccessibilityServiceInfo
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import android.view.accessibility.AccessibilityManager
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.coffee.chatbot.service.ChatbotAccessibilityService
import com.coffee.chatbot.service.FloatingWindowService
import com.coffee.chatbot.service.WebViewService
import com.coffee.chatbot.ui.theme.ChatbotTheme

class MainActivity : ComponentActivity() {
    
    private var isServiceRunning = false
    private var webServerUrl by mutableStateOf("")
    
    private var overlayPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (Settings.canDrawOverlays(this)) {
            // 如果服务尚未启动，则检查无障碍服务权限
            if (!isServiceRunning) {
                checkAccessibilityPermission()
            }
        } else {
            Toast.makeText(this, "需要悬浮窗权限才能显示浮动按钮", Toast.LENGTH_SHORT).show()
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        updateUI()
    }
    
    override fun onResume() {
        super.onResume()
        // 检查服务是否正在运行并更新UI状态
        isServiceRunning = isFloatingServiceRunning()
        updateWebServerUrl()
        updateUI()
    }
    
    private fun updateWebServerUrl() {
        val ipAddress = WebViewService.getLocalIpAddress()
        webServerUrl = "http://$ipAddress:8080"
    }
    
    private fun updateUI() {
        setContent {
            ChatbotTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    MainContent(
                        modifier = Modifier.padding(innerPadding),
                        serviceRunning = isServiceRunning,
                        webServerUrl = webServerUrl,
                        onStartService = {
                            checkAndRequestPermissions()
                        },
                        onStopService = {
                            stopServices()
                            isServiceRunning = false
                        }
                    )
                }
            }
        }
    }
    
    private fun isFloatingServiceRunning(): Boolean {
        // 简单的检查方法，在真实应用中可能需要更复杂的检查
        return Settings.canDrawOverlays(this) && isAccessibilityServiceEnabled()
    }
    
    private fun checkAndRequestPermissions() {
        // 检查悬浮窗权限
        if (!Settings.canDrawOverlays(this)) {
            requestOverlayPermission()
            return
        }
        
        // 检查无障碍服务权限
        checkAccessibilityPermission()
    }
    
    private fun checkAccessibilityPermission() {
        if (!isAccessibilityServiceEnabled()) {
            requestAccessibilityPermission()
            return
        }
        
        // 所有权限都已授予，启动服务
        startServices()
    }
    
    private fun requestOverlayPermission() {
        val intent = Intent(
            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
            Uri.parse("package:$packageName")
        )
        Toast.makeText(
            this,
            "请启用悬浮窗权限以显示浮动按钮",
            Toast.LENGTH_LONG
        ).show()
        overlayPermissionLauncher.launch(intent)
    }
    
    private fun requestAccessibilityPermission() {
        Toast.makeText(
            this,
            "请启用无障碍服务以提取聊天内容",
            Toast.LENGTH_LONG
        ).show()
        startActivity(Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS))
    }
    
    private fun isAccessibilityServiceEnabled(): Boolean {
        val serviceId = "${packageName}/${ChatbotAccessibilityService::class.java.canonicalName}"
        Log.d("MainActivity", "Checking for accessibility service: $serviceId")
        try {
            val enabledServices = Settings.Secure.getString(contentResolver, Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES)
            if (enabledServices != null) {
                val colonSplitter = TextUtils.SimpleStringSplitter(':')
                colonSplitter.setString(enabledServices)
                while (colonSplitter.hasNext()) {
                    val componentName = colonSplitter.next()
                    if (componentName.equals(serviceId, ignoreCase = true)) {
                        Log.d("MainActivity", "Accessibility service is enabled.")
                        return true
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Error checking for accessibility service", e)
        }
        Log.d("MainActivity", "Accessibility service is NOT enabled.")
        return false
    }
    
    private fun startServices() {
        // 启动悬浮窗服务
        val floatingIntent = Intent(this, FloatingWindowService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(floatingIntent)
        } else {
            startService(floatingIntent)
        }
        
        // 启动Web服务
        val webIntent = Intent(this, WebViewService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(webIntent)
        } else {
            startService(webIntent)
        }
        
        isServiceRunning = true
        updateWebServerUrl()
        updateUI()
        
        Toast.makeText(
            this,
            "服务已启动，现在可以导航到聊天应用了。",
            Toast.LENGTH_LONG
        ).show()
    }
    
    private fun stopServices() {
        // 停止悬浮窗服务
        val floatingIntent = Intent(this, FloatingWindowService::class.java)
        floatingIntent.action = "STOP_SERVICE"
        startService(floatingIntent)
        
        // 停止Web服务
        val webIntent = Intent(this, WebViewService::class.java)
        webIntent.action = "STOP_SERVICE"
        startService(webIntent)
        
        isServiceRunning = false
        updateUI()
        
        Toast.makeText(this, "服务已停止", Toast.LENGTH_SHORT).show()
    }
}

@Composable
fun MainContent(
    modifier: Modifier = Modifier, 
    serviceRunning: Boolean = false,
    webServerUrl: String = "",
    onStartService: () -> Unit,
    onStopService: () -> Unit
) {
    Column(
        modifier = modifier.fillMaxSize().padding(16.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "Chatbot 无障碍服务",
            fontSize = 24.sp,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        if (serviceRunning) {
            Text(
                text = "服务已启动",
                fontSize = 18.sp,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (webServerUrl.isNotEmpty()) {
                Text(
                    text = "Web服务地址:",
                    fontSize = 16.sp
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = webServerUrl,
                    fontSize = 18.sp,
                    color = MaterialTheme.colorScheme.tertiary
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "在浏览器中打开此地址查看节点层级",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            Button(
                onClick = onStopService,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text("停止服务")
            }
        } else {
            Text(
                text = "服务未启动",
                fontSize = 18.sp,
                color = MaterialTheme.colorScheme.error
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            Button(
                onClick = onStartService,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("启动服务")
            }
        }
    }
}