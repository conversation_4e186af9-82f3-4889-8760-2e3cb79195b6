package com.coffee.chatbot.service

import android.view.accessibility.AccessibilityNodeInfo
import java.util.regex.Pattern
import java.util.ArrayDeque

/**
 * 为 AccessibilityNodeInfo 提供优雅的 XPath 风格查询功能
 * 参考 Python lxml 库的设计理念，提供简洁流畅的 API
 *
 * 基本用法示例:
 * ```kotlin
 * // 查找节点
 * val nodes = rootNode.xpath("//TextView[@text='确定']")
 * val node = rootNode.xpath("//Button").first()
 *
 * // 链式操作
 * rootNode.xpath("//TextView[@text='确定']")
 *         .parent()
 *         .xpath(".//Button[@clickable='true']")
 *         .click()
 *
 * // 便捷方法
 * rootNode.find("TextView", text = "确定")?.click()
 * rootNode.findAll("Button", clickable = true).forEach { it.click() }
 * ```
 *
 * 支持的 XPath 语法:
 * - 基本路径: `/`, `//`, `..`, `.`
 * - 索引选择: `[0]`, `[last()]`, `[position()>1]`
 * - 属性匹配: `[@text='value']`, `[@text~='partial']`, `[@text?='regex']`
 * - 逻辑组合: `[condition1 and condition2]`, `[condition1 or condition2]`
 * - 通配符: `*` (匹配任何类名)
 *
 * 支持的属性简写:
 * - text, id, desc, pkg, cls
 * - clickable, scrollable, checked, selected, enabled, focused, visible
 */

// ================================================================================================
// 核心数据结构
// ================================================================================================

private data class PathStep(val axis: Axis, val segment: String)
private enum class Axis { SELF, CHILD, DESCENDANT, PARENT }

/**
 * XPath 查询结果包装类，提供链式操作支持
 */
class XPathResult(private val nodes: List<AccessibilityNodeInfo>) : List<AccessibilityNodeInfo> by nodes {

    /** 获取第一个节点 */
    fun first(): AccessibilityNodeInfo? = nodes.firstOrNull()

    /** 获取最后一个节点 */
    fun last(): AccessibilityNodeInfo? = nodes.lastOrNull()

    /** 获取指定索引的节点 */
    fun at(index: Int): AccessibilityNodeInfo? = nodes.getOrNull(index)

    /** 继续 XPath 查询 */
    fun xpath(expression: String): XPathResult {
        val results = mutableListOf<AccessibilityNodeInfo>()
        nodes.forEach { node ->
            results.addAll(node.findNodesByXPath(expression))
        }
        return XPathResult(results)
    }

    /** 获取父节点 */
    fun parent(): XPathResult {
        val parents = nodes.mapNotNull { it.parent }
        return XPathResult(parents)
    }

    /** 点击所有节点 */
    fun click(): Boolean {
        var success = true
        nodes.forEach { node ->
            if (!node.performAction(AccessibilityNodeInfo.ACTION_CLICK)) {
                success = false
            }
        }
        return success
    }

    /** 获取所有节点的文本 */
    fun texts(): List<String> = nodes.mapNotNull { it.text?.toString() }

    /** 获取第一个节点的文本 */
    fun text(): String? = first()?.text?.toString()

    /** 检查是否存在节点 */
    fun exists(): Boolean = nodes.isNotEmpty()

    /** 回收所有节点资源 */
    fun recycle() {
        nodes.forEach { it.recycle() }
    }
}

// ================================================================================================
// 主要 API 方法
// ================================================================================================

/**
 * 使用 XPath 表达式查找节点（主要方法）
 * @param xpath XPath 表达式字符串
 * @return XPathResult 包装的结果，支持链式操作
 */
fun AccessibilityNodeInfo.xpath(xpath: String): XPathResult {
    return XPathResult(findNodesByXPath(xpath))
}

/**
 * 内部实现：使用 XPath 表达式查找匹配的节点
 */
private fun AccessibilityNodeInfo.findNodesByXPath(xpath: String): List<AccessibilityNodeInfo> {
    val steps = tokenize(xpath)
    var currentNodes = setOf(this)

    for (step in steps) {
        val nextNodes = mutableSetOf<AccessibilityNodeInfo>()
        for (node in currentNodes) {
            when (step.axis) {
                Axis.PARENT -> {
                    node.parent?.let { nextNodes.add(it) }
                }
                Axis.SELF -> {
                    nextNodes.add(node)
                }
                Axis.CHILD, Axis.DESCENDANT -> {
                    val (className, predicates) = parseSegment(step.segment)
                    val candidates = if (step.axis == Axis.DESCENDANT) {
                        findAllDescendants(node)
                    } else {
                        (0 until node.childCount).mapNotNull { node.getChild(it) }
                    }

                    // 1. 先根据类名过滤
                    val nameMatchedCandidates = if (className.isEmpty() || className == "*") {
                        candidates
                    } else {
                        candidates.filter { it.className?.toString()?.endsWith(className) == true }
                    }

                    // 2. 再应用谓词
                    val finalMatched = if (predicates.isEmpty()) {
                        nameMatchedCandidates
                    } else {
                        nameMatchedCandidates.filterIndexed { index, child ->
                            matchesPredicates(child, index, predicates, nameMatchedCandidates.size)
                        }
                    }
                    nextNodes.addAll(finalMatched)

                    // 3. 回收未匹配的节点
                    candidates.filterNot { finalMatched.contains(it) }.forEach { it.recycle() }
                }
            }
        }
        currentNodes = nextNodes
    }

    return currentNodes.toList()
}

/**
 * 查找所有后代节点（不包括自身）
 * @param node 起始节点
 * @return 所有后代节点列表
 */
private fun findAllDescendants(node: AccessibilityNodeInfo): List<AccessibilityNodeInfo> {
    val result = mutableListOf<AccessibilityNodeInfo>()
    val queue = ArrayDeque<AccessibilityNodeInfo>()
    queue.addLast(node)
    
    while(queue.isNotEmpty()){
        val currentNode = queue.removeFirst()
        // 不把自己加进去，只加子孙
        // result.add(currentNode) 
        for (i in 0 until currentNode.childCount) {
            currentNode.getChild(i)?.let { child ->
                result.add(child)
                queue.addLast(child)
            }
        }
    }
    return result
}

/**
 * 将XPath字符串解析为步骤列表
 * @param xpath XPath表达式字符串
 * @return 解析后的路径步骤列表
 */
private fun tokenize(xpath: String): List<PathStep> {
    val path = xpath.trim()
    if (path.isBlank()) return emptyList()

    val steps = mutableListOf<PathStep>()
    val remainingPath: String
    val initialAxis: Axis

    when {
        path.startsWith("//") -> {
            initialAxis = Axis.DESCENDANT
            remainingPath = path.substring(2)
        }
        path.startsWith("/") -> {
            initialAxis = Axis.CHILD
            remainingPath = path.substring(1)
        }
        else -> {
            // Default to descendant for paths like "TextView[...]" or "../..."
            initialAxis = Axis.DESCENDANT
            remainingPath = path
        }
    }

    val segments = remainingPath.split('/').filter { it.isNotEmpty() }

    if (segments.isEmpty()) return emptyList()

    // Handle first segment with its initial axis.
    // If the path starts with '..', the axis must be PARENT.
    val firstSegment = segments[0]
    val firstAxis = if (firstSegment == "..") Axis.PARENT else initialAxis
    steps.add(PathStep(firstAxis, firstSegment))

    // Handle subsequent segments
    for (i in 1 until segments.size) {
        val segment = segments[i]
        if (segment.isNotEmpty()) {
            val axis = if (segment == "..") Axis.PARENT else Axis.CHILD
            steps.add(PathStep(axis, segment))
        }
    }
    return steps
}

/**
 * 解析路径片段，提取类名和谓词列表
 * @param segment 路径片段字符串
 * @return 类名和谓词列表的对
 */
private fun parseSegment(segment: String): Pair<String, List<String>> {
    // 处理特殊情况
    if (segment == "." || segment == "..") {
        return Pair(segment, emptyList())
    }
    
    val predicatePattern = Pattern.compile("\\[(.*?)\\]")
    val predicateMatcher = predicatePattern.matcher(segment)
    val predicates = mutableListOf<String>()
    while (predicateMatcher.find()) {
        predicates.add(predicateMatcher.group(1))
    }
    
    val className = segment.replace(predicatePattern.toRegex(), "")
    return Pair(className, predicates)
}

/**
 * 检查节点是否匹配所有谓词条件
 * @param node 要检查的节点
 * @param index 节点在兄弟节点中的索引
 * @param predicates 谓词条件列表
 * @param totalCount 兄弟节点总数
 * @return 是否匹配所有谓词
 */
private fun matchesPredicates(node: AccessibilityNodeInfo, index: Int, predicates: List<String>, totalCount: Int = 0): Boolean {
    if (predicates.isEmpty()) return true

    return predicates.all { predicate ->
        when {
            // 索引谓词, e.g., [0]
            predicate.matches(Regex("\\d+")) -> 
                index == predicate.toInt()
            
            // 位置函数谓词
            predicate.startsWith("position()") -> 
                evaluatePositionPredicate(predicate, index)
            
            // last()函数谓词
            predicate.startsWith("last()") -> 
                evaluateLastPredicate(predicate, index, totalCount)
            
            // 多条件组合谓词
            predicate.contains(" and ") -> {
                val conditions = predicate.split(" and ")
                conditions.all { matchesPredicates(node, index, listOf(it.trim()), totalCount) }
            }
            
            predicate.contains(" or ") -> {
                val conditions = predicate.split(" or ")
                conditions.any { matchesPredicates(node, index, listOf(it.trim()), totalCount) }
            }
            
            // not()函数谓词
            predicate.startsWith("not(") && predicate.endsWith(")") -> {
                val innerPredicate = predicate.substring(4, predicate.length - 1)
                !matchesPredicates(node, index, listOf(innerPredicate), totalCount)
            }
            
            // 属性谓词, e.g., [@text='确定']
            else -> {
                val attrPattern = Pattern.compile("@(\\w+)(==|!=|~=|\\^=|\\$=|\\?=)['\"](.*?)['\"]")
                val attrMatcher = attrPattern.matcher(predicate)
                if (attrMatcher.matches()) {
                    val attr = attrMatcher.group(1)
                    val operator = attrMatcher.group(2)
                    val value = attrMatcher.group(3)
                    matchesAttribute(node, attr, value, operator)
                } else {
                    // 尝试匹配简单属性谓词 [@attr='value']
                    val simpleAttrPattern = Pattern.compile("@(\\w+)=['\"](.*?)['\"]")
                    val simpleAttrMatcher = simpleAttrPattern.matcher(predicate)
                    if (simpleAttrMatcher.matches()) {
                        val attr = simpleAttrMatcher.group(1)
                        val value = simpleAttrMatcher.group(2)
                        matchesAttribute(node, attr, value, "==")
                    } else {
                        false // 不支持的谓词格式
                    }
                }
            }
        }
    }
}

/**
 * 评估位置谓词
 * @param predicate 谓词表达式
 * @param index 当前索引
 * @return 是否匹配位置谓词
 */
private fun evaluatePositionPredicate(predicate: String, index: Int): Boolean {
    val position = index + 1 // XPath position is 1-based

    // 使用正则表达式安全地提取操作符和数字
    val pattern = Pattern.compile("position\\(\\)\\s*([<>=!]+)\\s*(\\d+)")
    val matcher = pattern.matcher(predicate)

    if (matcher.find()) {
        val operator = matcher.group(1)
        val value = matcher.group(2).toIntOrNull() ?: return false

        return when (operator) {
            "=", "==" -> position == value
            "!=" -> position != value
            ">" -> position > value
            "<" -> position < value
            ">=" -> position >= value
            "<=" -> position <= value
            else -> false
        }
    }
    return false
}

/**
 * 评估last()函数谓词
 * @param predicate 谓词表达式
 * @param index 当前索引
 * @param totalCount 节点总数
 * @return 是否匹配last()谓词
 */
private fun evaluateLastPredicate(predicate: String, index: Int, totalCount: Int): Boolean {
    // last()
    if (predicate == "last()") {
        return index == totalCount - 1
    }
    // last() - n
    else if (predicate.contains("-")) {
        val offset = predicate.substringAfter("-").trim().toIntOrNull() ?: return false
        return index == totalCount - 1 - offset
    }
    return false
}

/**
 * 检查节点属性是否匹配指定值和操作符（优化版本）
 */
private fun matchesAttribute(node: AccessibilityNodeInfo, attr: String, value: String, operator: String = "=="): Boolean {
    val attrValue = getNodeAttribute(node, attr) ?: return false

    return when (operator) {
        "==" -> attrValue == value
        "!=" -> attrValue != value
        "~=" -> attrValue.contains(value)
        "^=" -> attrValue.startsWith(value)
        "$=" -> attrValue.endsWith(value)
        "?=" -> try { attrValue.matches(Regex(value)) } catch (e: Exception) { false }
        else -> false
    }
}

/**
 * 获取节点属性值（缓存优化）
 */
private fun getNodeAttribute(node: AccessibilityNodeInfo, attr: String): String? {
    return when (attr.lowercase()) {
        "text" -> node.text?.toString()
        "id" -> node.viewIdResourceName
        "desc" -> node.contentDescription?.toString()
        "pkg" -> node.packageName?.toString()
        "cls" -> node.className?.toString()
        "clickable" -> node.isClickable.toString()
        "scrollable" -> node.isScrollable.toString()
        "checked" -> node.isChecked.toString()
        "selected" -> node.isSelected.toString()
        "enabled" -> node.isEnabled.toString()
        "focused" -> node.isFocused.toString()
        "visible" -> node.isVisibleToUser.toString()
        else -> null
    }
}

// ================================================================================================
// 便捷查找方法 (类似 lxml 的简化语法)
// ================================================================================================

/**
 * 便捷方法：根据类名和属性查找单个节点
 * @param className 类名（可选，为空则匹配所有类）
 * @param text 文本内容（可选）
 * @param id 资源ID（可选）
 * @param desc 内容描述（可选）
 * @param clickable 是否可点击（可选）
 * @return 找到的第一个节点
 */
fun AccessibilityNodeInfo.find(
    className: String = "",
    text: String? = null,
    id: String? = null,
    desc: String? = null,
    clickable: Boolean? = null,
    scrollable: Boolean? = null,
    enabled: Boolean? = null
): AccessibilityNodeInfo? {
    val conditions = mutableListOf<String>()

    text?.let { conditions.add("@text='$it'") }
    id?.let { conditions.add("@id='$it'") }
    desc?.let { conditions.add("@desc='$it'") }
    clickable?.let { conditions.add("@clickable='$it'") }
    scrollable?.let { conditions.add("@scrollable='$it'") }
    enabled?.let { conditions.add("@enabled='$it'") }

    val predicate = if (conditions.isNotEmpty()) "[${conditions.joinToString(" and ")}]" else ""
    val xpath = "//${if (className.isEmpty()) "*" else className}$predicate"

    return xpath(xpath).first()
}

/**
 * 便捷方法：根据类名和属性查找所有节点
 */
fun AccessibilityNodeInfo.findAll(
    className: String = "",
    text: String? = null,
    id: String? = null,
    desc: String? = null,
    clickable: Boolean? = null,
    scrollable: Boolean? = null,
    enabled: Boolean? = null
): XPathResult {
    val conditions = mutableListOf<String>()

    text?.let { conditions.add("@text='$it'") }
    id?.let { conditions.add("@id='$it'") }
    desc?.let { conditions.add("@desc='$it'") }
    clickable?.let { conditions.add("@clickable='$it'") }
    scrollable?.let { conditions.add("@scrollable='$it'") }
    enabled?.let { conditions.add("@enabled='$it'") }

    val predicate = if (conditions.isNotEmpty()) "[${conditions.joinToString(" and ")}]" else ""
    val xpath = "//${if (className.isEmpty()) "*" else className}$predicate"

    return xpath(xpath)
}

/**
 * 便捷方法：根据文本查找节点
 */
fun AccessibilityNodeInfo.findByText(text: String, exact: Boolean = true): AccessibilityNodeInfo? {
    val operator = if (exact) "=" else "~="
    return xpath("//[@text${operator}'$text']").first()
}

/**
 * 便捷方法：根据文本查找所有节点
 */
fun AccessibilityNodeInfo.findAllByText(text: String, exact: Boolean = true): XPathResult {
    val operator = if (exact) "=" else "~="
    return xpath("//[@text${operator}'$text']")
}

/**
 * 便捷方法：根据ID查找节点
 */
fun AccessibilityNodeInfo.findById(id: String): AccessibilityNodeInfo? {
    return xpath("//[@id='$id']").first()
}

/**
 * 便捷方法：查找可点击的节点
 */
fun AccessibilityNodeInfo.findClickable(className: String = ""): XPathResult {
    val classPath = if (className.isEmpty()) "*" else className
    return xpath("//$classPath[@clickable='true']")
}

// ================================================================================================
// 兼容性方法 (保持向后兼容)
// ================================================================================================

/**
 * 兼容方法：查找单个节点
 */
fun AccessibilityNodeInfo.findNodeByXPath(xpath: String): AccessibilityNodeInfo? {
    return xpath(xpath).first()
}

/**
 * 兼容方法：查找多个节点
 */

/**
 * 兼容方法：检查是否存在匹配的节点
 */
fun AccessibilityNodeInfo.existsByXPath(xpath: String): Boolean {
    val result = xpath(xpath)
    val exists = result.exists()
    result.recycle()
    return exists
}

/**
 * 兼容方法：点击匹配的第一个节点
 */
fun AccessibilityNodeInfo.clickByXPath(xpath: String): Boolean {
    val result = xpath(xpath)
    val success = result.first()?.performAction(AccessibilityNodeInfo.ACTION_CLICK) ?: false
    result.recycle()
    return success
}

// ================================================================================================
// 高级便捷方法 (类似 lxml 的高级功能)
// ================================================================================================

/**
 * 使用 CSS 选择器风格的简化语法
 * 例如: node.select("TextView.clickable") 等价于 "//TextView[@clickable='true']"
 */
fun AccessibilityNodeInfo.select(selector: String): XPathResult {
    val xpath = convertCssSelectorToXPath(selector)
    return xpath(xpath)
}

/**
 * 将 CSS 选择器转换为 XPath
 */
private fun convertCssSelectorToXPath(selector: String): String {
    var xpath = selector

    // 处理类选择器 .clickable -> [@clickable='true']
    xpath = xpath.replace(Regex("\\.([a-zA-Z]+)")) { match ->
        "[@${match.groupValues[1]}='true']"
    }

    // 处理 ID 选择器 #myId -> [@id='myId']
    xpath = xpath.replace(Regex("#([a-zA-Z0-9_]+)")) { match ->
        "[@id='${match.groupValues[1]}']"
    }

    // 如果没有以 / 开头，添加 //
    if (!xpath.startsWith("/")) {
        xpath = "//$xpath"
    }

    return xpath
}

/**
 * 获取节点的完整 XPath 路径
 */
fun AccessibilityNodeInfo.getXPath(): String {
    val path = mutableListOf<String>()
    var current: AccessibilityNodeInfo? = this

    while (current != null) {
        val className = current.className?.toString()?.substringAfterLast('.') ?: "Unknown"
        val index = current.parent?.let { parent ->
            (0 until parent.childCount).count { i ->
                parent.getChild(i)?.className == current?.className
            }
        } ?: 0

        path.add(0, "$className[$index]")
        current = current.parent
    }

    return "/" + path.joinToString("/")
}

/**
 * 获取节点的简化描述信息
 */
fun AccessibilityNodeInfo.describe(): String {
    val className = className?.toString()?.substringAfterLast('.') ?: "Unknown"
    val text = text?.toString()?.let { "text='$it'" } ?: ""
    val id = viewIdResourceName?.let { "id='$it'" } ?: ""
    val desc = contentDescription?.toString()?.let { "desc='$it'" } ?: ""

    val attributes = listOfNotNull(text, id, desc).filter { it.isNotEmpty() }
    val attributeStr = if (attributes.isNotEmpty()) "[${attributes.joinToString(", ")}]" else ""

    return "$className$attributeStr"
}

/**
 * 安全执行操作，自动处理资源回收
 */
inline fun <T> AccessibilityNodeInfo.use(block: (AccessibilityNodeInfo) -> T): T {
    return try {
        block(this)
    } finally {
        // 注意：只回收非根节点
        if (parent != null) {
            recycle()
        }
    }
}

/**
 * 安全执行 XPath 查询，自动处理资源回收
 */
inline fun <T> AccessibilityNodeInfo.xpathUse(xpath: String, block: (XPathResult) -> T): T {
    val result = xpath(xpath)
    return try {
        block(result)
    } finally {
        result.recycle()
    }
}

/**
 * 调试方法：打印节点树结构
 */
fun AccessibilityNodeInfo.printTree(maxDepth: Int = 5, currentDepth: Int = 0) {
    if (currentDepth > maxDepth) return

    val indent = "  ".repeat(currentDepth)
    println("$indent${describe()}")

    for (i in 0 until childCount) {
        getChild(i)?.printTree(maxDepth, currentDepth + 1)
    }
}

/**
 * 查找发送按钮的多种策略
 */
fun AccessibilityNodeInfo.findSendButton(): AccessibilityNodeInfo? {
    // 策略1: 根据文本查找
    findByText("发送")?.let { return it }
    findByText("Send")?.let { return it }
    findByText("确定")?.let { return it }

    // 策略2: 根据描述查找
    find(desc = "发送消息")?.let { return it }
    find(desc = "发送")?.let { return it }

    // 策略3: 根据ID查找
    findById("send_button")?.let { return it }
    findById("btn_send")?.let { return it }

    // 策略4: 查找输入框附近的按钮
    val inputField = find(className = "EditText")
    return inputField?.xpath("..//*[@clickable='true' and @cls='Button']")?.first()
}