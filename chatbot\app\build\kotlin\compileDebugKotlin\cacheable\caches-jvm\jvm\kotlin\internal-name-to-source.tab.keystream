    c o m / c o f f e e / c h a t b o t / M a i n A c t i v i t y   * c o m / c o f f e e / c h a t b o t / M a i n A c t i v i t y $ u p d a t e U I $ 1   , c o m / c o f f e e / c h a t b o t / M a i n A c t i v i t y $ u p d a t e U I $ 1 $ 1   . c o m / c o f f e e / c h a t b o t / M a i n A c t i v i t y $ u p d a t e U I $ 1 $ 1 $ 1   6 c o m / c o f f e e / c h a t b o t / C o m p o s a b l e S i n g l e t o n s $ M a i n A c t i v i t y K t   A c o m / c o f f e e / c h a t b o t / C o m p o s a b l e S i n g l e t o n s $ M a i n A c t i v i t y K t $ l a m b d a - 1 $ 1   A c o m / c o f f e e / c h a t b o t / C o m p o s a b l e S i n g l e t o n s $ M a i n A c t i v i t y K t $ l a m b d a - 2 $ 1   ! c o m / c o f f e e / c h a t b o t / M a i n A c t i v i t y K t   $ c o m / c o f f e e / c h a t b o t / m o d e l / C h a t M e s s a g e   # c o m / c o f f e e / c h a t b o t / s e r v i c e / P a t h S t e p    c o m / c o f f e e / c h a t b o t / s e r v i c e / A x i s   < c o m / c o f f e e / c h a t b o t / s e r v i c e / A c c e s s i b i l i t y N o d e I n f o E x t e n s i o n s K t   I c o m / c o f f e e / c h a t b o t / s e r v i c e / A c c e s s i b i l i t y N o d e I n f o E x t e n s i o n s K t $ W h e n M a p p i n g s   6 c o m / c o f f e e / c h a t b o t / s e r v i c e / C h a t b o t A c c e s s i b i l i t y S e r v i c e   M c o m / c o f f e e / c h a t b o t / s e r v i c e / C h a t b o t A c c e s s i b i l i t y S e r v i c e $ o n A c c e s s i b i l i t y E v e n t $ 1   O c o m / c o f f e e / c h a t b o t / s e r v i c e / C h a t b o t A c c e s s i b i l i t y S e r v i c e $ o n A c c e s s i b i l i t y E v e n t $ 1 $ 1   O c o m / c o f f e e / c h a t b o t / s e r v i c e / C h a t b o t A c c e s s i b i l i t y S e r v i c e $ o n A c c e s s i b i l i t y E v e n t $ 1 $ 2   ] c o m / c o f f e e / c h a t b o t / s e r v i c e / C h a t b o t A c c e s s i b i l i t y S e r v i c e $ e x t r a c t C h a t H i s t o r y $ $ i n l i n e d $ s o r t e d B y $ 1   g c o m / c o f f e e / c h a t b o t / s e r v i c e / C h a t b o t A c c e s s i b i l i t y S e r v i c e $ s t a r t L i s t e n i n g F o r N e w M e s s a g e s $ $ i n l i n e d $ R u n n a b l e $ 1   L c o m / c o f f e e / c h a t b o t / s e r v i c e / C h a t b o t A c c e s s i b i l i t y S e r v i c e $ s t a r t C h a t E x t r a c t i o n $ 1   A c o m / c o f f e e / c h a t b o t / s e r v i c e / C h a t b o t A c c e s s i b i l i t y S e r v i c e $ s c r o l l U p $ 1   D c o m / c o f f e e / c h a t b o t / s e r v i c e / C h a t b o t A c c e s s i b i l i t y S e r v i c e $ s e n d M e s s a g e $ 1   @ c o m / c o f f e e / c h a t b o t / s e r v i c e / C h a t b o t A c c e s s i b i l i t y S e r v i c e $ C o m p a n i o n   C c o m / c o f f e e / c h a t b o t / s e r v i c e / C h a t b o t A c c e s s i b i l i t y S e r v i c e $ M e s s a g e G r o u p   1 c o m / c o f f e e / c h a t b o t / s e r v i c e / C u s t o m e r S e r v i c e H a n d l e r   @ c o m / c o f f e e / c h a t b o t / s e r v i c e / C u s t o m e r S e r v i c e H a n d l e r $ p e r f o r m C l i c k $ 1   ; c o m / c o f f e e / c h a t b o t / s e r v i c e / C u s t o m e r S e r v i c e H a n d l e r $ C o m p a n i o n   0 c o m / c o f f e e / c h a t b o t / s e r v i c e / F l o a t i n g W i n d o w S e r v i c e   G c o m / c o f f e e / c h a t b o t / s e r v i c e / F l o a t i n g W i n d o w S e r v i c e $ h a n d l e C h a t A u t o m a t i o n $ 1   : c o m / c o f f e e / c h a t b o t / s e r v i c e / F l o a t i n g W i n d o w S e r v i c e $ C o m p a n i o n   ) c o m / c o f f e e / c h a t b o t / s e r v i c e / W e b V i e w S e r v i c e   = c o m / c o f f e e / c h a t b o t / s e r v i c e / W e b V i e w S e r v i c e $ N o d e H i e r a r c h y S e r v e r   3 c o m / c o f f e e / c h a t b o t / s e r v i c e / W e b V i e w S e r v i c e $ C o m p a n i o n   # c o m / c o f f e e / c h a t b o t / u i / t h e m e / C o l o r K t   # c o m / c o f f e e / c h a t b o t / u i / t h e m e / T h e m e K t   " c o m / c o f f e e / c h a t b o t / u i / t h e m e / T y p e K t   & c o m / c o f f e e / c h a t b o t / s e r v i c e / X P a t h R e s u l t   G c o m / c o f f e e / c h a t b o t / s e r v i c e / C u s t o m e r S e r v i c e H a n d l e r $ p e r f o r m G e s t u r e C l i c k $ 1   3 c o m / c o f f e e / c h a t b o t / s e r v i c e / C u s t o m e r S e r v i c e H a n d l e r K t   - c o m / c o f f e e / c h a t b o t / s e r v i c e / X P a t h U s a g e E x a m p l e s   / c o m / c o f f e e / c h a t b o t / s e r v i c e / X P a t h U s a g e E x a m p l e s K t   O c o m / c o f f e e / c h a t b o t / s e r v i c e / C u s t o m e r S e r v i c e H a n d l e r $ p e r f o r m G e s t u r e C l i c k $ s u c c e s s $ 1   + c o m / c o f f e e / c h a t b o t / s e r v i c e / C h a t D e t a i l R e s u l t   Q c o m / c o f f e e / c h a t b o t / s e r v i c e / C u s t o m e r S e r v i c e H a n d l e r $ s t a r t C h a t D e t a i l P a g e M o n i t o r i n g $ 1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      